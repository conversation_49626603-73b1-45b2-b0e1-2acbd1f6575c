-- Project Madara OBF - Configuration Presets
-- Predefined configurations for different security levels and use cases

local Presets = {}

-- Security level presets
Presets.security_levels = {
    minimal = {
        string_obfuscation = {
            enabled = true,
            encryption_method = "xor",
            encryption_layers = 1,
            use_dynamic_keys = false,
            use_steganography = false,
            polymorphic_decryption = false
        },
        control_flow = {
            enabled = false,
            opaque_predicates = false,
            bogus_branches_ratio = 0,
            function_indirection = false,
            dead_code_insertion = false
        },
        variable_obfuscation = {
            enabled = true,
            naming_strategy = "random",
            use_homoglyphs = false,
            anti_pattern_analysis = false
        },
        anti_debug = {
            enabled = false,
            vm_detection = false,
            debugger_detection = false,
            integrity_checks = false
        },
        bytecode = {
            enabled = false
        }
    },
    
    standard = {
        string_obfuscation = {
            enabled = true,
            encryption_method = "multi_layer",
            encryption_layers = 2,
            use_dynamic_keys = true,
            use_steganography = false,
            polymorphic_decryption = true
        },
        control_flow = {
            enabled = true,
            opaque_predicates = true,
            bogus_branches_ratio = 0.2,
            function_indirection = true,
            dead_code_insertion = true
        },
        variable_obfuscation = {
            enabled = true,
            naming_strategy = "context_aware",
            use_homoglyphs = false,
            anti_pattern_analysis = true
        },
        anti_debug = {
            enabled = true,
            vm_detection = true,
            debugger_detection = true,
            integrity_checks = true,
            environment_fingerprinting = false
        },
        bytecode = {
            enabled = false
        }
    },
    
    enhanced = {
        string_obfuscation = {
            enabled = true,
            encryption_method = "multi_layer",
            encryption_layers = 3,
            use_dynamic_keys = true,
            use_steganography = true,
            polymorphic_decryption = true
        },
        control_flow = {
            enabled = true,
            opaque_predicates = true,
            bogus_branches_ratio = 0.3,
            function_indirection = true,
            dead_code_insertion = true,
            control_flow_flattening = true
        },
        variable_obfuscation = {
            enabled = true,
            naming_strategy = "context_aware",
            use_homoglyphs = true,
            anti_pattern_analysis = true
        },
        anti_debug = {
            enabled = true,
            vm_detection = true,
            debugger_detection = true,
            integrity_checks = true,
            environment_fingerprinting = true,
            self_modification = false,
            anti_hook = true,
            timing_checks = true
        },
        bytecode = {
            enabled = true,
            custom_vm = false,
            instruction_encryption = true,
            register_obfuscation = true
        }
    },
    
    maximum = {
        string_obfuscation = {
            enabled = true,
            encryption_method = "multi_layer",
            encryption_layers = 5,
            use_dynamic_keys = true,
            use_steganography = true,
            polymorphic_decryption = true
        },
        control_flow = {
            enabled = true,
            opaque_predicates = true,
            bogus_branches_ratio = 0.4,
            function_indirection = true,
            dead_code_insertion = true,
            control_flow_flattening = true
        },
        variable_obfuscation = {
            enabled = true,
            naming_strategy = "homoglyphs",
            use_homoglyphs = true,
            anti_pattern_analysis = true
        },
        anti_debug = {
            enabled = true,
            vm_detection = true,
            debugger_detection = true,
            integrity_checks = true,
            environment_fingerprinting = true,
            self_modification = true,
            anti_hook = true,
            timing_checks = true
        },
        bytecode = {
            enabled = true,
            custom_vm = true,
            instruction_encryption = true,
            register_obfuscation = true,
            execution_randomization = true,
            vm_nesting_levels = 2
        }
    }
}

-- Use case specific presets
Presets.use_cases = {
    -- For protecting commercial software
    commercial = {
        security_level = "enhanced",
        preserve_functionality = true,
        string_obfuscation = {
            encryption_method = "multi_layer",
            encryption_layers = 3,
            use_steganography = true
        },
        anti_debug = {
            vm_detection = true,
            debugger_detection = true,
            integrity_checks = true,
            environment_fingerprinting = true
        }
    },
    
    -- For game anti-cheat protection
    gaming = {
        security_level = "maximum",
        preserve_functionality = true,
        control_flow = {
            opaque_predicates = true,
            bogus_branches_ratio = 0.3,
            control_flow_flattening = true
        },
        anti_debug = {
            vm_detection = true,
            debugger_detection = true,
            integrity_checks = true,
            self_modification = true,
            timing_checks = true
        },
        bytecode = {
            enabled = true,
            custom_vm = true,
            vm_nesting_levels = 2
        }
    },
    
    -- For malware research (educational)
    research = {
        security_level = "maximum",
        preserve_functionality = false,
        string_obfuscation = {
            encryption_method = "steganographic",
            use_steganography = true,
            polymorphic_decryption = true
        },
        control_flow = {
            bogus_branches_ratio = 0.5,
            control_flow_flattening = true
        },
        anti_debug = {
            vm_detection = true,
            debugger_detection = true,
            self_modification = true,
            anti_hook = true
        }
    },
    
    -- For embedded systems
    embedded = {
        security_level = "standard",
        preserve_functionality = true,
        string_obfuscation = {
            encryption_method = "xor",
            encryption_layers = 1,
            use_dynamic_keys = false
        },
        control_flow = {
            enabled = true,
            opaque_predicates = false,
            bogus_branches_ratio = 0.1
        },
        bytecode = {
            enabled = false
        }
    },
    
    -- For web applications
    web = {
        security_level = "enhanced",
        preserve_functionality = true,
        string_obfuscation = {
            encryption_method = "multi_layer",
            encryption_layers = 2,
            use_steganography = false
        },
        control_flow = {
            opaque_predicates = true,
            function_indirection = true,
            dead_code_insertion = true
        },
        variable_obfuscation = {
            naming_strategy = "context_aware",
            use_homoglyphs = false
        }
    }
}

-- Platform specific presets
Presets.platforms = {
    windows = {
        anti_debug = {
            vm_detection = true,
            debugger_detection = true,
            environment_fingerprinting = true
        }
    },
    
    linux = {
        anti_debug = {
            vm_detection = true,
            debugger_detection = true,
            timing_checks = true
        }
    },
    
    macos = {
        anti_debug = {
            vm_detection = true,
            debugger_detection = true,
            integrity_checks = true
        }
    }
}

-- Competition specific presets (surpassing other obfuscators)
Presets.competitive = {
    -- Designed to surpass Prometheus
    anti_prometheus = {
        security_level = "enhanced",
        string_obfuscation = {
            encryption_method = "multi_layer",
            encryption_layers = 4,
            use_dynamic_keys = true,
            use_steganography = true,
            polymorphic_decryption = true
        },
        control_flow = {
            opaque_predicates = true,
            bogus_branches_ratio = 0.35,
            function_indirection = true,
            control_flow_flattening = true
        },
        variable_obfuscation = {
            naming_strategy = "homoglyphs",
            use_homoglyphs = true,
            anti_pattern_analysis = true
        },
        anti_debug = {
            vm_detection = true,
            debugger_detection = true,
            integrity_checks = true,
            environment_fingerprinting = true,
            self_modification = true,
            anti_hook = true
        },
        bytecode = {
            enabled = true,
            custom_vm = true,
            instruction_encryption = true,
            register_obfuscation = true,
            vm_nesting_levels = 2
        }
    },
    
    -- Designed to surpass other commercial obfuscators
    anti_commercial = {
        security_level = "maximum",
        string_obfuscation = {
            encryption_method = "steganographic",
            encryption_layers = 5,
            use_steganography = true,
            polymorphic_decryption = true
        },
        control_flow = {
            opaque_predicates = true,
            bogus_branches_ratio = 0.4,
            function_indirection = true,
            control_flow_flattening = true
        },
        anti_debug = {
            vm_detection = true,
            debugger_detection = true,
            integrity_checks = true,
            environment_fingerprinting = true,
            self_modification = true,
            anti_hook = true,
            timing_checks = true
        }
    }
}

-- Get list of all available presets
function Presets.list()
    local preset_list = {}
    
    -- Add security levels
    for name, _ in pairs(Presets.security_levels) do
        table.insert(preset_list, {
            name = name,
            category = "security_level",
            description = "Security level preset: " .. name
        })
    end
    
    -- Add use cases
    for name, _ in pairs(Presets.use_cases) do
        table.insert(preset_list, {
            name = name,
            category = "use_case",
            description = "Use case preset: " .. name
        })
    end
    
    -- Add competitive presets
    for name, _ in pairs(Presets.competitive) do
        table.insert(preset_list, {
            name = name,
            category = "competitive",
            description = "Competitive preset: " .. name
        })
    end
    
    return preset_list
end

-- Get preset by name
function Presets.get(preset_name)
    -- Check security levels
    if Presets.security_levels[preset_name] then
        return Presets.security_levels[preset_name]
    end
    
    -- Check use cases
    if Presets.use_cases[preset_name] then
        return Presets.use_cases[preset_name]
    end
    
    -- Check competitive presets
    if Presets.competitive[preset_name] then
        return Presets.competitive[preset_name]
    end
    
    return nil
end

-- Get preset with platform-specific adjustments
function Presets.get_for_platform(preset_name, platform)
    local preset = Presets.get(preset_name)
    if not preset then
        return nil
    end
    
    local platform_config = Presets.platforms[platform]
    if platform_config then
        -- Merge platform-specific settings
        local Utils = require("core.utils")
        preset = Utils.deep_merge(preset, platform_config)
    end
    
    return preset
end

return Presets
