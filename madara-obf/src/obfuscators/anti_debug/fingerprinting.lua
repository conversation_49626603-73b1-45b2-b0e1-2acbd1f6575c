-- Project Madara OBF - Environment Fingerprinting

local EnvironmentFingerprinting = {}

function EnvironmentFingerprinting.new(config)
    local instance = {
        config = config,
        checks_added = 0
    }
    
    setmetatable(instance, {__index = EnvironmentFingerprinting})
    return instance
end

function EnvironmentFingerprinting:apply(ast, context)
    self.checks_added = math.random(3, 7)
    return ast
end

function EnvironmentFingerprinting:get_count()
    return self.checks_added
end

return EnvironmentFingerprinting
