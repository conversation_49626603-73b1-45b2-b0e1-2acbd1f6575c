-- Project Madara OBF - Advanced String Obfuscation Module
-- Superior string obfuscation techniques that surpass Prometheus

local StringObfuscators = {}

local Utils = require("core.utils")
local Logger = require("core.logger")
local Pipeline = require("core.pipeline")

-- Import specific obfuscation techniques
local MultiLayerEncryption = require("obfuscators.strings.multi_layer")
local SteganographicObfuscation = require("obfuscators.strings.steganographic")
local PolymorphicDecryption = require("obfuscators.strings.polymorphic")
local DynamicKeyGeneration = require("obfuscators.strings.dynamic_keys")

-- String obfuscation methods
local OBFUSCATION_METHODS = {
    xor = "XOR Encryption",
    aes = "AES Encryption", 
    multi_layer = "Multi-Layer Encryption",
    steganographic = "Steganographic Hiding"
}

-- Create string obfuscation step
function StringObfuscators.create_step(config)
    local step = {
        name = "Advanced String Obfuscation",
        description = "Multi-layered string obfuscation with dynamic encryption",
        priority = Pipeline.PRIORITIES.STRING_OBFUSCATION,
        config = config,
        logger = Logger.new(config.log_level or "info"),
        
        -- Obfuscation components
        multi_layer = MultiLayerEncryption.new(config),
        steganographic = SteganographicObfuscation.new(config),
        polymorphic = PolymorphicDecryption.new(config),
        dynamic_keys = DynamicKeyGeneration.new(config),
        
        -- Statistics
        stats = {
            strings_processed = 0,
            encryption_layers_applied = 0,
            steganographic_strings = 0,
            polymorphic_decoders = 0
        }
    }
    
    setmetatable(step, {__index = StringObfuscators})
    return step
end

-- Apply string obfuscation to AST
function StringObfuscators:apply(context)
    self.logger:info("Applying advanced string obfuscation")
    
    local ast = context.ast
    local string_literals = self:_find_string_literals(ast)
    
    if #string_literals == 0 then
        self.logger:debug("No string literals found to obfuscate")
        return ast
    end
    
    self.logger:debug("Found " .. #string_literals .. " string literals to obfuscate")
    
    -- Generate encryption infrastructure
    local encryption_infrastructure = self:_generate_encryption_infrastructure(context)
    
    -- Process each string literal
    for _, string_node in ipairs(string_literals) do
        if self:_should_obfuscate_string(string_node) then
            self:_obfuscate_string_node(string_node, encryption_infrastructure, context)
            self.stats.strings_processed = self.stats.strings_processed + 1
        end
    end
    
    -- Inject decryption infrastructure into AST
    self:_inject_decryption_infrastructure(ast, encryption_infrastructure)
    
    self.logger:info("String obfuscation completed - processed " .. self.stats.strings_processed .. " strings")
    
    return ast
end

-- Find all string literals in AST
function StringObfuscators:_find_string_literals(ast)
    local string_literals = {}
    
    local function traverse(node)
        if node.type == "STRING_LITERAL" then
            table.insert(string_literals, node)
        end
        
        for _, child in ipairs(node.children or {}) do
            traverse(child)
        end
    end
    
    traverse(ast)
    return string_literals
end

-- Check if string should be obfuscated
function StringObfuscators:_should_obfuscate_string(string_node)
    local value = string_node.value or ""
    
    -- Skip very short strings if configured
    if #value < (self.config.min_string_length or 3) then
        return false
    end
    
    -- Skip strings that look like they might be important for functionality
    local skip_patterns = {
        "^%s*$",  -- Whitespace only
        "^[%w_]+$",  -- Simple identifiers
        "^%d+$"   -- Numbers as strings
    }
    
    for _, pattern in ipairs(skip_patterns) do
        if value:match(pattern) then
            return false
        end
    end
    
    return true
end

-- Generate encryption infrastructure
function StringObfuscators:_generate_encryption_infrastructure(context)
    local infrastructure = {
        encryption_keys = {},
        decryption_functions = {},
        steganographic_data = {},
        polymorphic_decoders = {}
    }
    
    -- Generate dynamic encryption keys
    if self.config.use_dynamic_keys then
        infrastructure.encryption_keys = self.dynamic_keys:generate_keys(context)
    end
    
    -- Create polymorphic decryption functions
    if self.config.polymorphic_decryption then
        infrastructure.polymorphic_decoders = self.polymorphic:generate_decoders(context)
        self.stats.polymorphic_decoders = #infrastructure.polymorphic_decoders
    end
    
    return infrastructure
end

-- Obfuscate individual string node
function StringObfuscators:_obfuscate_string_node(string_node, infrastructure, context)
    local original_value = string_node.value
    local obfuscated_data = {}
    
    -- Apply encryption method based on configuration
    if self.config.encryption_method == "multi_layer" then
        obfuscated_data = self.multi_layer:encrypt(original_value, infrastructure)
        self.stats.encryption_layers_applied = self.stats.encryption_layers_applied + self.config.encryption_layers
        
    elseif self.config.encryption_method == "steganographic" then
        obfuscated_data = self.steganographic:hide_string(original_value, infrastructure)
        self.stats.steganographic_strings = self.stats.steganographic_strings + 1
        
    elseif self.config.encryption_method == "aes" then
        obfuscated_data = self:_aes_encrypt(original_value, infrastructure)
        
    else
        -- Fallback to XOR
        obfuscated_data = self:_xor_encrypt(original_value, infrastructure)
    end
    
    -- Replace string node with decryption call
    self:_replace_with_decryption_call(string_node, obfuscated_data, infrastructure)
end

-- Simple XOR encryption (fallback)
function StringObfuscators:_xor_encrypt(text, infrastructure)
    local key = infrastructure.encryption_keys[1] or 42
    local encrypted = {}
    
    for i = 1, #text do
        local char_code = string.byte(text, i)
        local encrypted_char = char_code ~ (key + i - 1)
        table.insert(encrypted, encrypted_char)
    end
    
    return {
        method = "xor",
        data = encrypted,
        key = key
    }
end

-- AES encryption (simplified implementation)
function StringObfuscators:_aes_encrypt(text, infrastructure)
    -- This would be a proper AES implementation in production
    -- For now, using a more complex XOR variant
    local key = infrastructure.encryption_keys[1] or Utils.secure_random_bytes(16)
    local encrypted = {}
    
    for i = 1, #text do
        local char_code = string.byte(text, i)
        local key_byte = key[(i - 1) % #key + 1]
        local encrypted_char = char_code ~ key_byte ~ (i * 7)
        table.insert(encrypted, encrypted_char)
    end
    
    return {
        method = "aes",
        data = encrypted,
        key = key
    }
end

-- Replace string node with decryption call
function StringObfuscators:_replace_with_decryption_call(string_node, obfuscated_data, infrastructure)
    -- Create function call node for decryption
    local decryption_call = {
        type = "FUNCTION_CALL",
        id = "decrypt_" .. Utils.generate_uuid():gsub("-", ""),
        function_name = "decrypt_string",
        arguments = {
            {
                type = "TABLE_LITERAL",
                value = obfuscated_data.data
            },
            {
                type = "NUMBER_LITERAL", 
                value = obfuscated_data.key
            }
        }
    }
    
    -- Replace the original string node
    if string_node.parent then
        for i, child in ipairs(string_node.parent.children) do
            if child == string_node then
                string_node.parent.children[i] = decryption_call
                decryption_call.parent = string_node.parent
                break
            end
        end
    end
end

-- Inject decryption infrastructure into AST
function StringObfuscators:_inject_decryption_infrastructure(ast, infrastructure)
    -- Create decryption function
    local decryption_function = self:_create_decryption_function(infrastructure)
    
    -- Find the main block and inject at the beginning
    local main_block = self:_find_main_block(ast)
    if main_block then
        table.insert(main_block.statements, 1, decryption_function)
    end
end

-- Create decryption function based on encryption method
function StringObfuscators:_create_decryption_function(infrastructure)
    local function_body = [[
local function decrypt_string(data, key)
    local result = {}
    for i = 1, #data do
        local encrypted_char = data[i]
        local decrypted_char = encrypted_char ~ (key + i - 1)
        table.insert(result, string.char(decrypted_char))
    end
    return table.concat(result)
end
]]
    
    return {
        type = "FUNCTION_DECLARATION",
        name = "decrypt_string",
        body = function_body,
        source_code = function_body
    }
end

-- Find main block in AST
function StringObfuscators:_find_main_block(ast)
    if ast.type == "TOP_LEVEL" and ast.children and #ast.children > 0 then
        return ast.children[1]
    end
    return nil
end

-- Get obfuscation statistics
function StringObfuscators:get_statistics()
    return Utils.deep_copy(self.stats)
end

return StringObfuscators
