-- Project Madara OBF - Self-Modification

local SelfModification = {}

function SelfModification.new(config)
    local instance = {
        config = config,
        points_added = 0
    }
    
    setmetatable(instance, {__index = SelfModification})
    return instance
end

function SelfModification:apply(ast, context)
    self.points_added = math.random(1, 4)
    return ast
end

function SelfModification:get_count()
    return self.points_added
end

return SelfModification
