-- Project Madara OBF - Code Analyzer
-- Advanced code analysis for optimization and security assessment

local CodeAnalyzer = {}

local Utils = require("core.utils")
local Logger = require("core.logger")

-- Create new code analyzer instance
function CodeAnalyzer.new()
    local instance = {
        logger = Logger.new("info")
    }
    
    setmetatable(instance, {__index = CodeAnalyzer})
    return instance
end

-- Analyze source code
function CodeAnalyzer:analyze(source_code)
    local analysis = {
        complexity = self:_calculate_complexity(source_code),
        strings = self:_analyze_strings(source_code),
        functions = self:_analyze_functions(source_code),
        variables = self:_analyze_variables(source_code),
        control_flow = self:_analyze_control_flow(source_code),
        security_risks = self:_identify_security_risks(source_code)
    }
    
    return analysis
end

-- Calculate code complexity
function CodeAnalyzer:_calculate_complexity(source_code)
    local complexity = {
        lines = 0,
        functions = 0,
        conditionals = 0,
        loops = 0,
        cyclomatic = 1
    }
    
    -- Count lines
    for line in source_code:gmatch("[^\r\n]+") do
        complexity.lines = complexity.lines + 1
    end
    
    -- Count functions
    for match in source_code:gmatch("function%s+") do
        complexity.functions = complexity.functions + 1
    end
    
    -- Count conditionals
    for match in source_code:gmatch("if%s+") do
        complexity.conditionals = complexity.conditionals + 1
        complexity.cyclomatic = complexity.cyclomatic + 1
    end
    
    -- Count loops
    for match in source_code:gmatch("for%s+") do
        complexity.loops = complexity.loops + 1
        complexity.cyclomatic = complexity.cyclomatic + 1
    end
    
    for match in source_code:gmatch("while%s+") do
        complexity.loops = complexity.loops + 1
        complexity.cyclomatic = complexity.cyclomatic + 1
    end
    
    return complexity
end

-- Analyze strings
function CodeAnalyzer:_analyze_strings(source_code)
    local strings = {
        count = 0,
        total_length = 0,
        patterns = {}
    }
    
    -- Find string literals
    for str in source_code:gmatch('"([^"]*)"') do
        strings.count = strings.count + 1
        strings.total_length = strings.total_length + #str
        
        -- Categorize strings
        if str:match("^[%w_]+$") then
            strings.patterns.identifiers = (strings.patterns.identifiers or 0) + 1
        elseif str:match("%d+") then
            strings.patterns.numeric = (strings.patterns.numeric or 0) + 1
        elseif str:match("[!@#$%%^&*()]+") then
            strings.patterns.special = (strings.patterns.special or 0) + 1
        end
    end
    
    return strings
end

-- Analyze functions
function CodeAnalyzer:_analyze_functions(source_code)
    local functions = {
        count = 0,
        local_functions = 0,
        global_functions = 0,
        anonymous_functions = 0
    }
    
    -- Count different function types
    for match in source_code:gmatch("local%s+function") do
        functions.local_functions = functions.local_functions + 1
    end
    
    for match in source_code:gmatch("function%s+[%w_%.]+") do
        functions.global_functions = functions.global_functions + 1
    end
    
    for match in source_code:gmatch("function%s*%(") do
        functions.anonymous_functions = functions.anonymous_functions + 1
    end
    
    functions.count = functions.local_functions + functions.global_functions + functions.anonymous_functions
    
    return functions
end

-- Analyze variables
function CodeAnalyzer:_analyze_variables(source_code)
    local variables = {
        local_vars = 0,
        global_vars = 0,
        table_accesses = 0
    }
    
    -- Count local variable declarations
    for match in source_code:gmatch("local%s+[%w_]") do
        variables.local_vars = variables.local_vars + 1
    end
    
    -- Count table accesses
    for match in source_code:gmatch("[%w_]+%.[%w_]+") do
        variables.table_accesses = variables.table_accesses + 1
    end
    
    return variables
end

-- Analyze control flow
function CodeAnalyzer:_analyze_control_flow(source_code)
    local control_flow = {
        if_statements = 0,
        for_loops = 0,
        while_loops = 0,
        repeat_loops = 0,
        nested_depth = 0
    }
    
    -- Count control structures
    for match in source_code:gmatch("if%s+") do
        control_flow.if_statements = control_flow.if_statements + 1
    end
    
    for match in source_code:gmatch("for%s+") do
        control_flow.for_loops = control_flow.for_loops + 1
    end
    
    for match in source_code:gmatch("while%s+") do
        control_flow.while_loops = control_flow.while_loops + 1
    end
    
    for match in source_code:gmatch("repeat%s+") do
        control_flow.repeat_loops = control_flow.repeat_loops + 1
    end
    
    return control_flow
end

-- Identify security risks
function CodeAnalyzer:_identify_security_risks(source_code)
    local risks = {}
    
    -- Check for potentially dangerous functions
    local dangerous_functions = {
        "loadstring", "load", "dofile", "loadfile",
        "os.execute", "io.popen", "debug.getinfo"
    }
    
    for _, func in ipairs(dangerous_functions) do
        if source_code:find(func) then
            table.insert(risks, {
                type = "dangerous_function",
                function_name = func,
                severity = "high"
            })
        end
    end
    
    -- Check for hardcoded credentials
    if source_code:find("password") or source_code:find("secret") or source_code:find("key") then
        table.insert(risks, {
            type = "potential_credentials",
            severity = "medium"
        })
    end
    
    return risks
end

return CodeAnalyzer
