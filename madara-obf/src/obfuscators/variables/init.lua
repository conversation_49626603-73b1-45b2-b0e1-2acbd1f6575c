-- Project Madara OBF - Advanced Variable Name Obfuscation
-- Context-aware naming with homoglyphs and anti-pattern analysis

local VariableObfuscators = {}

local Utils = require("core.utils")
local Logger = require("core.logger")
local Pipeline = require("core.pipeline")

-- Import specific obfuscation techniques
local ContextAwareNaming = require("obfuscators.variables.context_aware")
local HomoglyphSubstitution = require("obfuscators.variables.homoglyphs")
local AntiPatternAnalysis = require("obfuscators.variables.anti_pattern")
local SemanticPreservation = require("obfuscators.variables.semantic")

-- Create variable obfuscation step
function VariableObfuscators.create_step(config)
    local step = {
        name = "Advanced Variable Name Obfuscation",
        description = "Context-aware variable naming with homoglyphs and anti-pattern resistance",
        priority = Pipeline.PRIORITIES.VARIABLE_OBFUSCATION,
        config = config,
        logger = Logger.new(config.log_level or "info"),
        
        -- Obfuscation components
        context_aware = ContextAwareNaming.new(config),
        homoglyphs = HomoglyphSubstitution.new(config),
        anti_pattern = AntiPatternAnalysis.new(config),
        semantic = SemanticPreservation.new(config),
        
        -- Variable mapping
        variable_map = {},
        
        -- Statistics
        stats = {
            variables_renamed = 0,
            homoglyphs_used = 0,
            context_patterns_applied = 0,
            anti_patterns_detected = 0,
            semantic_hints_preserved = 0
        }
    }
    
    setmetatable(step, {__index = VariableObfuscators})
    return step
end

-- Apply variable obfuscation to AST
function VariableObfuscators:apply(context)
    self.logger:info("Applying advanced variable name obfuscation")
    
    local ast = context.ast
    
    -- Phase 1: Analyze variable usage patterns
    local variable_analysis = self:_analyze_variables(ast)
    
    -- Phase 2: Generate obfuscated names based on strategy
    local naming_strategy = self.config.naming_strategy or "context_aware"
    
    for var_name, var_info in pairs(variable_analysis) do
        local obfuscated_name = self:_generate_obfuscated_name(var_name, var_info, naming_strategy)
        self.variable_map[var_name] = obfuscated_name
        self.stats.variables_renamed = self.stats.variables_renamed + 1
    end
    
    -- Phase 3: Apply anti-pattern analysis
    if self.config.anti_pattern_analysis then
        self.variable_map = self.anti_pattern:analyze_and_adjust(self.variable_map)
        self.stats.anti_patterns_detected = self.anti_pattern:get_detections()
    end
    
    -- Phase 4: Apply variable renaming to AST
    ast = self:_apply_variable_renaming(ast)
    
    self.logger:info("Variable obfuscation completed - renamed " .. 
                    self.stats.variables_renamed .. " variables")
    
    return ast
end

-- Analyze variables in AST
function VariableObfuscators:_analyze_variables(ast)
    local variables = {}
    
    local function analyze_node(node, scope_type)
        scope_type = scope_type or "global"
        
        if node.type == "VARIABLE" or node.type == "LOCAL_DECLARATION" then
            local var_name = node.name or node.id
            if var_name and not variables[var_name] then
                variables[var_name] = {
                    name = var_name,
                    scope = scope_type,
                    usage_count = 0,
                    contexts = {},
                    type_hints = {},
                    semantic_category = self:_categorize_variable(var_name)
                }
            end
            
            if variables[var_name] then
                variables[var_name].usage_count = variables[var_name].usage_count + 1
                table.insert(variables[var_name].contexts, node.type)
            end
        end
        
        -- Determine scope for child nodes
        local child_scope = scope_type
        if node.type == "FUNCTION_DECLARATION" then
            child_scope = "function"
        elseif node.type == "BLOCK" then
            child_scope = "block"
        end
        
        for _, child in ipairs(node.children or {}) do
            analyze_node(child, child_scope)
        end
    end
    
    analyze_node(ast)
    return variables
end

-- Categorize variable semantically
function VariableObfuscators:_categorize_variable(var_name)
    local categories = {
        counter = {"i", "j", "k", "index", "idx", "count", "counter"},
        data = {"data", "value", "val", "item", "element", "obj", "object"},
        string = {"str", "text", "message", "msg", "name", "title"},
        number = {"num", "number", "amount", "size", "length", "len"},
        boolean = {"flag", "bool", "is", "has", "can", "should"},
        function = {"func", "fn", "callback", "handler", "method"},
        table = {"table", "list", "array", "map", "dict", "collection"},
        temp = {"temp", "tmp", "temporary", "buffer", "cache"}
    }
    
    local lower_name = var_name:lower()
    
    for category, patterns in pairs(categories) do
        for _, pattern in ipairs(patterns) do
            if lower_name:find(pattern) then
                return category
            end
        end
    end
    
    return "generic"
end

-- Generate obfuscated name based on strategy
function VariableObfuscators:_generate_obfuscated_name(var_name, var_info, strategy)
    if strategy == "context_aware" then
        return self.context_aware:generate_name(var_name, var_info)
        
    elseif strategy == "homoglyphs" then
        local base_name = self.context_aware:generate_name(var_name, var_info)
        return self.homoglyphs:apply_homoglyphs(base_name)
        
    elseif strategy == "random" then
        return self:_generate_random_name()
        
    elseif strategy == "mangled" then
        return self:_generate_mangled_name(var_name)
        
    else
        return self.context_aware:generate_name(var_name, var_info)
    end
end

-- Generate random variable name
function VariableObfuscators:_generate_random_name()
    local prefixes = {"_", "__", "l", "ll", "lll", "I", "Il", "II"}
    local chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_"
    
    local prefix = prefixes[math.random(#prefixes)]
    local length = math.random(self.config.min_name_length or 3, self.config.max_name_length or 20)
    
    local name = prefix
    for i = 1, length do
        local char_index = math.random(#chars)
        name = name .. chars:sub(char_index, char_index)
    end
    
    return name
end

-- Generate mangled variable name
function VariableObfuscators:_generate_mangled_name(original_name)
    local hash = Utils.calculate_hash(original_name)
    local chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    
    local name = ""
    local remaining = math.abs(hash)
    
    -- Convert hash to base-52 using letters
    repeat
        local index = (remaining % #chars) + 1
        name = chars:sub(index, index) .. name
        remaining = math.floor(remaining / #chars)
    until remaining == 0
    
    -- Ensure minimum length
    while #name < (self.config.min_name_length or 3) do
        name = chars:sub(math.random(#chars), math.random(#chars)) .. name
    end
    
    return "_" .. name
end

-- Apply variable renaming to AST
function VariableObfuscators:_apply_variable_renaming(ast)
    local function rename_in_node(node)
        if node.type == "VARIABLE" and node.name and self.variable_map[node.name] then
            node.name = self.variable_map[node.name]
            node.obfuscated = true
            
        elseif node.type == "LOCAL_DECLARATION" and node.id and self.variable_map[node.id] then
            node.id = self.variable_map[node.id]
            node.obfuscated = true
        end
        
        for _, child in ipairs(node.children or {}) do
            rename_in_node(child)
        end
    end
    
    rename_in_node(ast)
    return ast
end

-- Generate variable names that resist pattern analysis
function VariableObfuscators:_generate_anti_pattern_name(var_info)
    -- Use techniques that make automated analysis difficult
    local techniques = {
        "mixed_case_confusion",
        "similar_length_distribution", 
        "semantic_misdirection",
        "unicode_normalization_tricks"
    }
    
    local technique = techniques[math.random(#techniques)]
    
    if technique == "mixed_case_confusion" then
        return self:_generate_mixed_case_name()
    elseif technique == "similar_length_distribution" then
        return self:_generate_length_distributed_name()
    elseif technique == "semantic_misdirection" then
        return self:_generate_semantically_misleading_name(var_info)
    else
        return self:_generate_unicode_normalized_name()
    end
end

-- Generate mixed case name that confuses analysis
function VariableObfuscators:_generate_mixed_case_name()
    local patterns = {
        "lIlIlI", "IlIlIl", "llIIll", "IIllII",
        "oO0Oo", "O0oO0", "oo00oo", "OO00OO"
    }
    
    local base = patterns[math.random(#patterns)]
    local suffix = math.random(100, 999)
    
    return base .. suffix
end

-- Generate semantically misleading name
function VariableObfuscators:_generate_semantically_misleading_name(var_info)
    local misleading_names = {
        counter = {"data", "result", "output", "value"},
        data = {"index", "count", "flag", "temp"},
        string = {"number", "size", "length", "bool"},
        number = {"text", "message", "name", "str"},
        boolean = {"array", "list", "table", "obj"}
    }
    
    local category = var_info.semantic_category
    local misleading = misleading_names[category] or misleading_names.data
    local base_name = misleading[math.random(#misleading)]
    
    return base_name .. "_" .. math.random(10, 99)
end

-- Get obfuscation statistics
function VariableObfuscators:get_statistics()
    return Utils.deep_copy(self.stats)
end

-- Get variable mapping
function VariableObfuscators:get_variable_map()
    return Utils.deep_copy(self.variable_map)
end

return VariableObfuscators
