-- Project Madara OBF - Advanced Control Flow Obfuscation
-- Opaque predicates, bogus control flow, and function call indirection

local ControlFlowObfuscators = {}

local Utils = require("core.utils")
local Logger = require("core.logger")
local Pipeline = require("core.pipeline")

-- Import specific obfuscation techniques
local OpaquePredicates = require("obfuscators.control_flow.opaque_predicates")
local BogusControlFlow = require("obfuscators.control_flow.bogus_flow")
local FunctionIndirection = require("obfuscators.control_flow.function_indirection")
local ControlFlowFlattening = require("obfuscators.control_flow.flattening")

-- Create control flow obfuscation step
function ControlFlowObfuscators.create_step(config)
    local step = {
        name = "Advanced Control Flow Obfuscation",
        description = "Multi-technique control flow obfuscation with opaque predicates",
        priority = Pipeline.PRIORITIES.CONTROL_FLOW,
        config = config,
        logger = Logger.new(config.log_level or "info"),
        
        -- Obfuscation components
        opaque_predicates = OpaquePredicates.new(config),
        bogus_flow = BogusControlFlow.new(config),
        function_indirection = FunctionIndirection.new(config),
        flattening = ControlFlowFlattening.new(config),
        
        -- Statistics
        stats = {
            opaque_predicates_added = 0,
            bogus_branches_added = 0,
            functions_redirected = 0,
            control_structures_flattened = 0,
            dead_code_blocks_added = 0
        }
    }
    
    setmetatable(step, {__index = ControlFlowObfuscators})
    return step
end

-- Apply control flow obfuscation to AST
function ControlFlowObfuscators:apply(context)
    self.logger:info("Applying advanced control flow obfuscation")
    
    local ast = context.ast
    
    -- Phase 1: Add opaque predicates
    if self.config.opaque_predicates then
        self.logger:debug("Adding opaque predicates")
        ast = self.opaque_predicates:apply(ast, context)
        self.stats.opaque_predicates_added = self.opaque_predicates:get_count()
    end
    
    -- Phase 2: Insert bogus control flow
    if self.config.bogus_branches_ratio and self.config.bogus_branches_ratio > 0 then
        self.logger:debug("Inserting bogus control flow")
        ast = self.bogus_flow:apply(ast, context)
        self.stats.bogus_branches_added = self.bogus_flow:get_count()
    end
    
    -- Phase 3: Apply function call indirection
    if self.config.function_indirection then
        self.logger:debug("Applying function call indirection")
        ast = self.function_indirection:apply(ast, context)
        self.stats.functions_redirected = self.function_indirection:get_count()
    end
    
    -- Phase 4: Control flow flattening
    if self.config.control_flow_flattening then
        self.logger:debug("Applying control flow flattening")
        ast = self.flattening:apply(ast, context)
        self.stats.control_structures_flattened = self.flattening:get_count()
    end
    
    -- Phase 5: Insert dead code
    if self.config.dead_code_insertion then
        self.logger:debug("Inserting dead code blocks")
        ast = self:_insert_dead_code(ast, context)
    end
    
    self.logger:info("Control flow obfuscation completed - " .. 
                    self.stats.opaque_predicates_added .. " predicates, " ..
                    self.stats.bogus_branches_added .. " bogus branches, " ..
                    self.stats.functions_redirected .. " redirected functions")
    
    return ast
end

-- Insert dead code blocks
function ControlFlowObfuscators:_insert_dead_code(ast, context)
    local dead_code_templates = {
        "local _dead_var_%d = math.random(1000); if _dead_var_%d < 0 then print('unreachable') end",
        "local _temp_%d = {}; for _i_%d = 1, 0 do table.insert(_temp_%d, _i_%d) end",
        "local _dummy_%d = function() return false end; if _dummy_%d() then error('never') end",
        "local _check_%d = os.time(); if _check_%d < 0 then os.exit(1) end"
    }
    
    local function insert_dead_code_in_block(block)
        if not block.statements then return end
        
        local new_statements = {}
        
        for i, stmt in ipairs(block.statements) do
            table.insert(new_statements, stmt)
            
            -- Randomly insert dead code
            if math.random() < 0.1 then -- 10% chance
                local template = dead_code_templates[math.random(#dead_code_templates)]
                local id = math.random(10000)
                local dead_code = string.format(template, id, id, id, id)
                
                table.insert(new_statements, {
                    type = "DEAD_CODE",
                    source_code = dead_code,
                    id = "dead_" .. id
                })
                
                self.stats.dead_code_blocks_added = self.stats.dead_code_blocks_added + 1
            end
        end
        
        block.statements = new_statements
    end
    
    -- Traverse AST and insert dead code
    local function traverse(node)
        if node.type == "BLOCK" then
            insert_dead_code_in_block(node)
        end
        
        for _, child in ipairs(node.children or {}) do
            traverse(child)
        end
    end
    
    traverse(ast)
    return ast
end

-- Get obfuscation statistics
function ControlFlowObfuscators:get_statistics()
    return Utils.deep_copy(self.stats)
end

return ControlFlowObfuscators
