-- Project Madara OBF - Opaque Predicates
-- Complex mathematical conditions that always evaluate to known values

local OpaquePredicates = {}

local Utils = require("core.utils")
local Logger = require("core.logger")

-- Create new opaque predicates instance
function OpaquePredicates.new(config)
    local instance = {
        config = config,
        logger = Logger.new(config.log_level or "info"),
        predicates_added = 0,
        
        -- Opaque predicate templates (always true)
        always_true_predicates = {
            "(%d * %d) %% 2 == 0 or (%d * %d) %% 2 == 1", -- Even or odd
            "math.abs(%d - %d) >= 0", -- Absolute difference always >= 0
            "(%d + %d) * (%d - %d) == %d * %d - %d * %d", -- Algebraic identity
            "math.floor(%d / 1) == %d", -- Floor division by 1
            "string.len(tostring(%d)) > 0", -- String length always > 0
            "(%d ^ 2) >= 0", -- Square always non-negative
            "math.max(%d, %d) >= math.min(%d, %d)", -- Max >= Min
            "(%d + 1) > %d", -- Successor always greater
        },
        
        -- Opaque predicate templates (always false)
        always_false_predicates = {
            "(%d * %d) %% 2 == 2", -- Modulo 2 can't be 2
            "math.abs(%d) < 0", -- Absolute value can't be negative
            "(%d + 1) < %d", -- Successor can't be less
            "string.len('') > 0", -- Empty string length
            "math.floor(%d) > %d", -- Floor can't be greater than original
            "(%d ^ 2) < 0", -- Square can't be negative
            "math.max(%d, %d) < math.min(%d, %d)", -- Max < Min impossible
            "(%d - %d) ~= 0 and (%d - %d) == 0", -- Contradiction
        }
    }
    
    setmetatable(instance, {__index = OpaquePredicates})
    return instance
end

-- Apply opaque predicates to AST
function OpaquePredicates:apply(ast, context)
    self.logger:debug("Adding opaque predicates to control structures")
    
    local function add_predicates_to_node(node)
        if self:_should_add_predicate(node) then
            self:_add_opaque_predicate(node)
        end
        
        for _, child in ipairs(node.children or {}) do
            add_predicates_to_node(child)
        end
    end
    
    add_predicates_to_node(ast)
    
    self.logger:debug("Added " .. self.predicates_added .. " opaque predicates")
    return ast
end

-- Check if we should add predicate to this node
function OpaquePredicates:_should_add_predicate(node)
    -- Add predicates to control structures
    local control_types = {
        "IF_STATEMENT",
        "WHILE_LOOP", 
        "FOR_LOOP",
        "FUNCTION_DECLARATION"
    }
    
    for _, type in ipairs(control_types) do
        if node.type == type then
            return math.random() < 0.3 -- 30% chance
        end
    end
    
    return false
end

-- Add opaque predicate to node
function OpaquePredicates:_add_opaque_predicate(node)
    local predicate_type = math.random(2) -- 1 = always true, 2 = always false
    local predicate_code
    
    if predicate_type == 1 then
        predicate_code = self:_generate_always_true_predicate()
    else
        predicate_code = self:_generate_always_false_predicate()
    end
    
    -- Create opaque predicate wrapper
    local wrapper = {
        type = "OPAQUE_PREDICATE",
        predicate_type = predicate_type == 1 and "always_true" or "always_false",
        condition = predicate_code,
        original_node = node,
        id = "opaque_" .. self.predicates_added
    }
    
    -- Wrap the original node
    if predicate_type == 1 then
        -- Always true: if (opaque_true) then original_code end
        wrapper.wrapper_code = string.format([[
if %s then
    %s
end]], predicate_code, self:_node_to_code(node))
    else
        -- Always false: if (opaque_false) then dummy else original_code end
        wrapper.wrapper_code = string.format([[
if %s then
    -- This branch never executes
    error("Opaque predicate violation")
else
    %s
end]], predicate_code, self:_node_to_code(node))
    end
    
    -- Replace node with wrapper
    if node.parent then
        for i, child in ipairs(node.parent.children) do
            if child == node then
                node.parent.children[i] = wrapper
                wrapper.parent = node.parent
                break
            end
        end
    end
    
    self.predicates_added = self.predicates_added + 1
end

-- Generate always true predicate
function OpaquePredicates:_generate_always_true_predicate()
    local template = self.always_true_predicates[math.random(#self.always_true_predicates)]
    local values = {}
    
    -- Generate random values for template
    for i = 1, 8 do -- Support up to 8 placeholders
        table.insert(values, math.random(1, 100))
    end
    
    return string.format(template, unpack(values))
end

-- Generate always false predicate
function OpaquePredicates:_generate_always_false_predicate()
    local template = self.always_false_predicates[math.random(#self.always_false_predicates)]
    local values = {}
    
    -- Generate random values for template
    for i = 1, 8 do -- Support up to 8 placeholders
        table.insert(values, math.random(1, 100))
    end
    
    return string.format(template, unpack(values))
end

-- Convert node to code (simplified)
function OpaquePredicates:_node_to_code(node)
    if node.source_code then
        return node.source_code
    elseif node.type == "IF_STATEMENT" then
        return "-- if statement"
    elseif node.type == "WHILE_LOOP" then
        return "-- while loop"
    elseif node.type == "FOR_LOOP" then
        return "-- for loop"
    elseif node.type == "FUNCTION_DECLARATION" then
        return "-- function declaration"
    else
        return "-- " .. (node.type or "unknown")
    end
end

-- Generate complex mathematical opaque predicates
function OpaquePredicates:_generate_complex_predicate()
    local complex_templates = {
        -- Fermat's Little Theorem based
        "(%d ^ %d) %% %d == (%d %% %d)",
        
        -- Quadratic residue based
        "((%d * %d + %d) ^ 2) %% %d ~= 0",
        
        -- Number theory based
        "math.gcd(%d, %d) * math.lcm(%d, %d) == %d * %d",
        
        -- Trigonometric identities
        "math.abs(math.sin(%d) ^ 2 + math.cos(%d) ^ 2 - 1) < 0.0001",
        
        -- Bitwise operations
        "(%d & %d) | (%d & ~%d) == %d",
        
        -- Polynomial identities
        "(%d + %d) ^ 2 == %d ^ 2 + 2 * %d * %d + %d ^ 2"
    }
    
    local template = complex_templates[math.random(#complex_templates)]
    local values = {}
    
    for i = 1, 10 do
        table.insert(values, math.random(1, 50))
    end
    
    return string.format(template, unpack(values))
end

-- Generate environment-dependent predicates
function OpaquePredicates:_generate_environment_predicate()
    local env_templates = {
        "os.time() > 0",
        "string.len(tostring({})) > 0",
        "type(print) == 'function'",
        "math.random() >= 0 and math.random() <= 1",
        "_VERSION ~= nil",
        "type(_G) == 'table'",
        "pcall ~= nil",
        "string.byte('A') == 65"
    }
    
    return env_templates[math.random(#env_templates)]
end

-- Get count of added predicates
function OpaquePredicates:get_count()
    return self.predicates_added
end

return OpaquePredicates
