-- Project Madara OBF - Context-Aware Variable Naming

local ContextAwareNaming = {}

local Utils = require("core.utils")

function ContextAwareNaming.new(config)
    local instance = {
        config = config,
        naming_patterns = {
            counter = {"idx", "cnt", "iter", "pos"},
            data = {"val", "obj", "item", "elem"},
            string = {"txt", "msg", "str", "buf"},
            number = {"num", "amt", "qty", "len"},
            boolean = {"flg", "chk", "tst", "ok"},
            function = {"fn", "cb", "hnd", "proc"},
            table = {"tbl", "lst", "arr", "map"},
            temp = {"tmp", "aux", "wrk", "buf"}
        }
    }
    
    setmetatable(instance, {__index = ContextAwareNaming})
    return instance
end

function ContextAwareNaming:generate_name(var_name, var_info)
    local category = var_info.semantic_category or "generic"
    local patterns = self.naming_patterns[category] or self.naming_patterns.data
    
    local base = patterns[math.random(#patterns)]
    local suffix = math.random(10, 999)
    
    return "_" .. base .. suffix
end

return ContextAwareNaming
