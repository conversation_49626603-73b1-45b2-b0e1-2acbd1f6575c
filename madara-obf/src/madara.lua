-- Project Madara OBF - Advanced Lua Obfuscator
-- Main entry point and API
-- 
-- This file provides the primary interface for the Madara obfuscator,
-- offering a superior alternative to Prometheus with enhanced security features.

local MadaraOBF = {}

-- Version information
MadaraOBF.VERSION = "1.0.0"
MadaraOBF.NAME = "Project Madara OBF"
MadaraOBF.DESCRIPTION = "Advanced Lua Obfuscator with Military-Grade Protection"

-- Configure module path for internal requires
local function get_script_path()
    local str = debug.getinfo(2, "S").source:sub(2)
    return str:match("(.*[/%\\])")
end

local old_package_path = package.path
package.path = get_script_path() .. "?.lua;" .. 
               get_script_path() .. "core/?.lua;" .. 
               get_script_path() .. "obfuscators/?.lua;" .. 
               get_script_path() .. "analyzers/?.lua;" .. 
               get_script_path() .. "generators/?.lua;" .. 
               package.path

-- Core modules
local Pipeline = require("core.pipeline")
local Logger = require("core.logger")
local Config = require("core.config")
local Presets = require("config.presets")
local Utils = require("core.utils")

-- Obfuscation modules
local StringObfuscators = require("obfuscators.strings")
local ControlFlowObfuscators = require("obfuscators.control_flow")
local VariableObfuscators = require("obfuscators.variables")
local AntiDebugObfuscators = require("obfuscators.anti_debug")
local BytecodeObfuscators = require("obfuscators.bytecode")

-- Analysis modules
local CodeAnalyzer = require("analyzers.code_analyzer")
local SecurityAnalyzer = require("analyzers.security_analyzer")

-- Restore package path
package.path = old_package_path

-- Default configuration
MadaraOBF.DefaultConfig = {
    lua_version = "5.1",
    target_environment = "generic",
    security_level = "standard",
    preserve_functionality = true,
    enable_logging = true,
    log_level = "info",
    
    -- String obfuscation settings
    string_obfuscation = {
        enabled = true,
        encryption_method = "multi_layer",
        encryption_layers = 2,
        use_dynamic_keys = true,
        use_steganography = false,
        polymorphic_decryption = true
    },
    
    -- Control flow obfuscation settings
    control_flow = {
        enabled = true,
        opaque_predicates = true,
        bogus_branches_ratio = 0.2,
        function_indirection = true,
        loop_unrolling = false,
        dead_code_insertion = true
    },
    
    -- Variable obfuscation settings
    variable_obfuscation = {
        enabled = true,
        naming_strategy = "context_aware",
        use_homoglyphs = false,
        preserve_semantics = true,
        anti_pattern_analysis = true
    },
    
    -- Anti-debugging settings
    anti_debug = {
        enabled = true,
        vm_detection = true,
        debugger_detection = true,
        integrity_checks = true,
        environment_fingerprinting = false,
        self_modification = false
    },
    
    -- Bytecode obfuscation settings
    bytecode = {
        enabled = false,
        custom_vm = false,
        instruction_encryption = false,
        register_obfuscation = false,
        execution_randomization = false
    }
}

-- Security level presets
MadaraOBF.SecurityLevels = {
    minimal = "minimal",
    standard = "standard", 
    enhanced = "enhanced",
    maximum = "maximum"
}

-- Create new obfuscator instance
function MadaraOBF.new(config)
    config = config or {}
    
    -- Merge with default configuration
    local merged_config = Utils.deep_merge(MadaraOBF.DefaultConfig, config)
    
    -- Apply preset if specified
    if config.preset then
        local preset_config = Presets[config.preset]
        if preset_config then
            merged_config = Utils.deep_merge(merged_config, preset_config)
        else
            Logger:warn("Unknown preset: " .. tostring(config.preset))
        end
    end
    
    -- Apply security level preset
    if config.security_level then
        local level_config = Presets.security_levels[config.security_level]
        if level_config then
            merged_config = Utils.deep_merge(merged_config, level_config)
        end
    end
    
    local instance = {
        config = merged_config,
        pipeline = Pipeline.new(merged_config),
        logger = Logger.new(merged_config.log_level),
        analyzer = CodeAnalyzer.new(),
        security_analyzer = SecurityAnalyzer.new()
    }
    
    setmetatable(instance, {__index = MadaraOBF})
    
    -- Initialize pipeline with obfuscation steps
    instance:_initialize_pipeline()
    
    return instance
end

-- Initialize the obfuscation pipeline
function MadaraOBF:_initialize_pipeline()
    local config = self.config
    
    -- Add obfuscation steps based on configuration
    if config.string_obfuscation.enabled then
        self.pipeline:add_step(StringObfuscators.create_step(config.string_obfuscation))
    end
    
    if config.control_flow.enabled then
        self.pipeline:add_step(ControlFlowObfuscators.create_step(config.control_flow))
    end
    
    if config.variable_obfuscation.enabled then
        self.pipeline:add_step(VariableObfuscators.create_step(config.variable_obfuscation))
    end
    
    if config.anti_debug.enabled then
        self.pipeline:add_step(AntiDebugObfuscators.create_step(config.anti_debug))
    end
    
    if config.bytecode.enabled then
        self.pipeline:add_step(BytecodeObfuscators.create_step(config.bytecode))
    end
end

-- Process source code through obfuscation pipeline
function MadaraOBF:process(source_code, filename)
    filename = filename or "anonymous"
    
    self.logger:info("Starting obfuscation of " .. filename)
    
    -- Pre-processing analysis
    local analysis_result = self.analyzer:analyze(source_code)
    self.logger:debug("Code analysis completed")
    
    -- Security analysis
    local security_assessment = self.security_analyzer:assess(source_code, self.config)
    self.logger:debug("Security assessment completed")
    
    -- Apply obfuscation pipeline
    local obfuscated_code = self.pipeline:process(source_code, filename, analysis_result)
    
    -- Post-processing validation
    if self.config.preserve_functionality then
        local validation_result = self:_validate_obfuscation(source_code, obfuscated_code)
        if not validation_result.success then
            self.logger:error("Obfuscation validation failed: " .. validation_result.error)
            return nil, validation_result.error
        end
    end
    
    self.logger:info("Obfuscation completed successfully")
    
    return obfuscated_code, {
        analysis = analysis_result,
        security = security_assessment,
        statistics = self.pipeline:get_statistics()
    }
end

-- Validate that obfuscated code maintains functionality
function MadaraOBF:_validate_obfuscation(original, obfuscated)
    -- Basic syntax validation
    local success, error_msg = loadstring(obfuscated)
    if not success then
        return {success = false, error = "Syntax error in obfuscated code: " .. tostring(error_msg)}
    end
    
    -- Additional validation can be added here
    return {success = true}
end

-- Get available presets
function MadaraOBF.get_presets()
    return Presets.list()
end

-- Get version information
function MadaraOBF.get_version()
    return {
        version = MadaraOBF.VERSION,
        name = MadaraOBF.NAME,
        description = MadaraOBF.DESCRIPTION
    }
end

-- Export the module
return MadaraOBF
