-- Project Madara OBF - Custom Bytecode VM
-- Multi-layer virtual machine architecture with encrypted instructions

local BytecodeObfuscators = {}

local Utils = require("core.utils")
local Logger = require("core.logger")
local Pipeline = require("core.pipeline")

-- Import VM components
local CustomVM = require("obfuscators.bytecode.custom_vm")
local InstructionEncryption = require("obfuscators.bytecode.instruction_encryption")
local RegisterObfuscation = require("obfuscators.bytecode.register_obfuscation")
local ExecutionRandomization = require("obfuscators.bytecode.execution_randomization")

-- Custom instruction set
local CUSTOM_OPCODES = {
    -- Arithmetic operations
    MADD = 0x01,  -- Masked addition
    MSUB = 0x02,  -- Masked subtraction
    MMUL = 0x03,  -- Masked multiplication
    MDIV = 0x04,  -- Masked division
    
    -- Logical operations
    MAND = 0x05,  -- Masked AND
    MOR = 0x06,   -- Masked OR
    MXOR = 0x07,  -- Masked XOR
    MNOT = 0x08,  -- Masked NOT
    
    -- Memory operations
    MLOAD = 0x10,  -- Masked load
    MSTORE = 0x11, -- Masked store
    MPUSH = 0x12,  -- Masked push
    MPOP = 0x13,   -- Masked pop
    
    -- Control flow
    MJMP = 0x20,   -- Masked jump
    MJZ = 0x21,    -- Masked jump if zero
    MJNZ = 0x22,   -- Masked jump if not zero
    MCALL = 0x23,  -- Masked call
    MRET = 0x24,   -- Masked return
    
    -- String operations
    MSTRLEN = 0x30, -- Masked string length
    MSTRCAT = 0x31, -- Masked string concatenation
    MSTRCMP = 0x32, -- Masked string comparison
    
    -- Obfuscation operations
    MDECRYPT = 0x40, -- Decrypt operation
    MVERIFY = 0x41,  -- Integrity verification
    MMORPH = 0x42,   -- Code morphing
    
    -- VM control
    MHALT = 0xFF     -- Halt VM
}

-- Create bytecode obfuscation step
function BytecodeObfuscators.create_step(config)
    local step = {
        name = "Custom Bytecode VM",
        description = "Multi-layer virtual machine with encrypted instructions",
        priority = Pipeline.PRIORITIES.BYTECODE,
        config = config,
        logger = Logger.new(config.log_level or "info"),
        
        -- VM components
        custom_vm = CustomVM.new(config),
        instruction_encryption = InstructionEncryption.new(config),
        register_obfuscation = RegisterObfuscation.new(config),
        execution_randomization = ExecutionRandomization.new(config),
        
        -- VM state
        vm_nesting_level = config.vm_nesting_levels or 1,
        instruction_set = CUSTOM_OPCODES,
        
        -- Statistics
        stats = {
            instructions_generated = 0,
            instructions_encrypted = 0,
            registers_obfuscated = 0,
            vm_layers_created = 0,
            execution_paths_randomized = 0
        }
    }
    
    setmetatable(step, {__index = BytecodeObfuscators})
    return step
end

-- Apply bytecode obfuscation to AST
function BytecodeObfuscators:apply(context)
    self.logger:info("Applying custom bytecode VM obfuscation")
    
    local ast = context.ast
    
    -- Phase 1: Compile AST to custom bytecode
    local bytecode = self:_compile_to_bytecode(ast)
    self.stats.instructions_generated = #bytecode
    
    -- Phase 2: Encrypt instructions
    if self.config.instruction_encryption then
        bytecode = self.instruction_encryption:encrypt_instructions(bytecode)
        self.stats.instructions_encrypted = #bytecode
    end
    
    -- Phase 3: Obfuscate registers
    if self.config.register_obfuscation then
        bytecode = self.register_obfuscation:obfuscate_registers(bytecode)
        self.stats.registers_obfuscated = self.register_obfuscation:get_count()
    end
    
    -- Phase 4: Randomize execution paths
    if self.config.execution_randomization then
        bytecode = self.execution_randomization:randomize_execution(bytecode)
        self.stats.execution_paths_randomized = self.execution_randomization:get_count()
    end
    
    -- Phase 5: Create nested VMs
    for level = 1, self.vm_nesting_level do
        bytecode = self:_create_vm_layer(bytecode, level)
        self.stats.vm_layers_created = self.stats.vm_layers_created + 1
    end
    
    -- Phase 6: Generate VM runtime
    local vm_runtime = self:_generate_vm_runtime(bytecode)
    
    -- Replace AST with VM runtime
    local vm_ast = {
        type = "VM_RUNTIME",
        bytecode = bytecode,
        runtime_code = vm_runtime,
        vm_layers = self.vm_nesting_level,
        id = "custom_vm_root"
    }
    
    self.logger:info("Bytecode VM obfuscation completed - " .. 
                    self.stats.instructions_generated .. " instructions, " ..
                    self.stats.vm_layers_created .. " VM layers")
    
    return vm_ast
end

-- Compile AST to custom bytecode
function BytecodeObfuscators:_compile_to_bytecode(ast)
    local bytecode = {}
    local register_counter = 0
    local label_counter = 0
    
    local function get_next_register()
        register_counter = register_counter + 1
        return "R" .. register_counter
    end
    
    local function get_next_label()
        label_counter = label_counter + 1
        return "L" .. label_counter
    end
    
    local function compile_node(node)
        if node.type == "STRING_LITERAL" then
            local reg = get_next_register()
            table.insert(bytecode, {
                opcode = CUSTOM_OPCODES.MLOAD,
                dest = reg,
                value = node.value,
                type = "string"
            })
            return reg
            
        elseif node.type == "NUMBER_LITERAL" then
            local reg = get_next_register()
            table.insert(bytecode, {
                opcode = CUSTOM_OPCODES.MLOAD,
                dest = reg,
                value = node.value,
                type = "number"
            })
            return reg
            
        elseif node.type == "BINARY_OPERATION" then
            local left_reg = compile_node(node.left)
            local right_reg = compile_node(node.right)
            local result_reg = get_next_register()
            
            local opcode = CUSTOM_OPCODES.MADD -- Default
            if node.operator == "+" then
                opcode = CUSTOM_OPCODES.MADD
            elseif node.operator == "-" then
                opcode = CUSTOM_OPCODES.MSUB
            elseif node.operator == "*" then
                opcode = CUSTOM_OPCODES.MMUL
            elseif node.operator == "/" then
                opcode = CUSTOM_OPCODES.MDIV
            end
            
            table.insert(bytecode, {
                opcode = opcode,
                dest = result_reg,
                src1 = left_reg,
                src2 = right_reg
            })
            return result_reg
            
        elseif node.type == "FUNCTION_CALL" then
            local args = {}
            for _, arg in ipairs(node.arguments or {}) do
                table.insert(args, compile_node(arg))
            end
            
            local result_reg = get_next_register()
            table.insert(bytecode, {
                opcode = CUSTOM_OPCODES.MCALL,
                dest = result_reg,
                function_name = node.function_name,
                args = args
            })
            return result_reg
            
        elseif node.type == "IF_STATEMENT" then
            local condition_reg = compile_node(node.condition)
            local else_label = get_next_label()
            local end_label = get_next_label()
            
            table.insert(bytecode, {
                opcode = CUSTOM_OPCODES.MJZ,
                condition = condition_reg,
                target = else_label
            })
            
            -- Compile then branch
            for _, stmt in ipairs(node.then_branch or {}) do
                compile_node(stmt)
            end
            
            table.insert(bytecode, {
                opcode = CUSTOM_OPCODES.MJMP,
                target = end_label
            })
            
            -- Else label
            table.insert(bytecode, {
                opcode = "LABEL",
                name = else_label
            })
            
            -- Compile else branch
            for _, stmt in ipairs(node.else_branch or {}) do
                compile_node(stmt)
            end
            
            -- End label
            table.insert(bytecode, {
                opcode = "LABEL",
                name = end_label
            })
        end
        
        -- Compile child nodes
        for _, child in ipairs(node.children or {}) do
            compile_node(child)
        end
    end
    
    compile_node(ast)
    
    -- Add halt instruction
    table.insert(bytecode, {
        opcode = CUSTOM_OPCODES.MHALT
    })
    
    return bytecode
end

-- Create VM layer
function BytecodeObfuscators:_create_vm_layer(bytecode, level)
    local layer_key = Utils.secure_random_bytes(16)
    
    -- Encrypt bytecode for this layer
    local encrypted_bytecode = {}
    for i, instruction in ipairs(bytecode) do
        local encrypted_instruction = self:_encrypt_instruction(instruction, layer_key, i)
        table.insert(encrypted_bytecode, encrypted_instruction)
    end
    
    -- Wrap in VM layer
    local vm_layer = {
        level = level,
        key = layer_key,
        bytecode = encrypted_bytecode,
        interpreter = self:_generate_interpreter_for_level(level)
    }
    
    return vm_layer
end

-- Encrypt single instruction
function BytecodeObfuscators:_encrypt_instruction(instruction, key, index)
    local serialized = self:_serialize_instruction(instruction)
    local encrypted = {}
    
    for i = 1, #serialized do
        local byte = string.byte(serialized, i)
        local key_byte = key[(i - 1) % #key + 1]
        local encrypted_byte = byte ~ key_byte ~ (index * 7)
        table.insert(encrypted, encrypted_byte)
    end
    
    return {
        encrypted_data = encrypted,
        checksum = Utils.calculate_hash(serialized),
        index = index
    }
end

-- Serialize instruction to string
function BytecodeObfuscators:_serialize_instruction(instruction)
    local parts = {}
    
    table.insert(parts, tostring(instruction.opcode or 0))
    table.insert(parts, tostring(instruction.dest or ""))
    table.insert(parts, tostring(instruction.src1 or ""))
    table.insert(parts, tostring(instruction.src2 or ""))
    table.insert(parts, tostring(instruction.value or ""))
    
    return table.concat(parts, "|")
end

-- Generate VM runtime
function BytecodeObfuscators:_generate_vm_runtime(bytecode_layers)
    return string.format([[
-- Project Madara OBF - Custom VM Runtime
-- Multi-layer virtual machine with encrypted instructions

local function create_madara_vm()
    local vm = {
        registers = {},
        stack = {},
        memory = {},
        pc = 1, -- Program counter
        running = true,
        layers = %d
    }
    
    -- Initialize registers
    for i = 1, 256 do
        vm.registers[i] = 0
    end
    
    -- Instruction decoder
    local function decode_instruction(encrypted_data, key, index)
        local decrypted = {}
        for i = 1, #encrypted_data do
            local encrypted_byte = encrypted_data[i]
            local key_byte = key[(i - 1) %% #key + 1]
            local decrypted_byte = encrypted_byte ~ key_byte ~ (index * 7)
            table.insert(decrypted, string.char(decrypted_byte))
        end
        
        local serialized = table.concat(decrypted)
        local parts = {}
        for part in serialized:gmatch("[^|]+") do
            table.insert(parts, part)
        end
        
        return {
            opcode = tonumber(parts[1]) or 0,
            dest = parts[2] ~= "" and parts[2] or nil,
            src1 = parts[3] ~= "" and parts[3] or nil,
            src2 = parts[4] ~= "" and parts[4] or nil,
            value = parts[5] ~= "" and parts[5] or nil
        }
    end
    
    -- Instruction executor
    local function execute_instruction(instruction)
        local opcode = instruction.opcode
        
        if opcode == 0x01 then -- MADD
            local val1 = vm.registers[instruction.src1] or 0
            local val2 = vm.registers[instruction.src2] or 0
            vm.registers[instruction.dest] = val1 + val2
            
        elseif opcode == 0x02 then -- MSUB
            local val1 = vm.registers[instruction.src1] or 0
            local val2 = vm.registers[instruction.src2] or 0
            vm.registers[instruction.dest] = val1 - val2
            
        elseif opcode == 0x10 then -- MLOAD
            vm.registers[instruction.dest] = instruction.value
            
        elseif opcode == 0x20 then -- MJMP
            vm.pc = instruction.target
            return true -- Skip PC increment
            
        elseif opcode == 0xFF then -- MHALT
            vm.running = false
            
        end
        
        return false
    end
    
    -- VM execution loop
    function vm:run(bytecode_layers)
        while self.running do
            -- Multi-layer decryption
            local current_layer = bytecode_layers
            for level = self.layers, 1, -1 do
                if current_layer.level == level then
                    local encrypted_instruction = current_layer.bytecode[self.pc]
                    if not encrypted_instruction then
                        self.running = false
                        break
                    end
                    
                    local instruction = decode_instruction(
                        encrypted_instruction.encrypted_data,
                        current_layer.key,
                        encrypted_instruction.index
                    )
                    
                    local skip_increment = execute_instruction(instruction)
                    if not skip_increment then
                        self.pc = self.pc + 1
                    end
                    
                    break
                end
            end
        end
    end
    
    return vm
end

-- Create and run VM
local madara_vm = create_madara_vm()
local bytecode_data = %s
madara_vm:run(bytecode_data)
]], self.vm_nesting_level, self:_serialize_bytecode_layers(bytecode_layers))
end

-- Serialize bytecode layers for embedding
function BytecodeObfuscators:_serialize_bytecode_layers(layers)
    -- This would serialize the bytecode layers into Lua code
    -- For now, return a placeholder
    return "{level=1, key={}, bytecode={}}"
end

-- Get obfuscation statistics
function BytecodeObfuscators:get_statistics()
    return Utils.deep_copy(self.stats)
end

return BytecodeObfuscators
