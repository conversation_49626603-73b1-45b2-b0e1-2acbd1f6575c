#!/usr/bin/env lua

-- Project Madara OBF - Command Line Interface
-- Advanced CLI with comprehensive options and user-friendly interface

-- Configure package path
local function get_script_path()
    local str = debug.getinfo(2, "S").source:sub(2)
    return str:match("(.*[/%\\])")
end

package.path = get_script_path() .. "src/?.lua;" .. package.path

-- Import Madara OBF
local MadaraOBF = require("madara")
local Presets = require("config.presets")
local Utils = require("core.utils")

-- CLI Configuration
local CLI = {
    version = MadaraOBF.VERSION,
    name = "Madara OBF CLI",
    description = "Advanced Lua Obfuscator - Superior to Prometheus"
}

-- ANSI Colors for output
local Colors = {
    RESET = "\27[0m",
    BOLD = "\27[1m",
    RED = "\27[31m",
    GREEN = "\27[32m",
    YELLOW = "\27[33m",
    BLUE = "\27[34m",
    MAGENTA = "\27[35m",
    CYAN = "\27[36m"
}

local use_colors = true

-- Colored output function
local function colored_print(text, color)
    if use_colors and color then
        print(color .. text .. Colors.RESET)
    else
        print(text)
    end
end

-- Print banner
local function print_banner()
    colored_print([[
 ███▄ ▄███▓ ▄▄▄      ▓█████▄  ▄▄▄       ██▀███   ▄▄▄          ▒█████   ▄▄▄▄    █████▒
▓██▒▀█▀ ██▒▒████▄    ▒██▀ ██▌▒████▄    ▓██ ▒ ██▒▒████▄       ▒██▒  ██▒▓█████▄ ▓██   ▒ 
▓██    ▓██░▒██  ▀█▄  ░██   █▌▒██  ▀█▄  ▓██ ░▄█ ▒▒██  ▀█▄     ▒██░  ██▒▒██▒ ▄██▒████ ░ 
▒██    ▒██ ░██▄▄▄▄██ ░▓█▄   ▌░██▄▄▄▄██ ▒██▀▀█▄  ░██▄▄▄▄██    ▒██   ██░▒██░█▀  ░▓█▒  ░ 
▒██▒   ░██▒ ▓█   ▓██▒░▒████▓  ▓█   ▓██▒░██▓ ▒██▒ ▓█   ▓██▒   ░ ████▓▒░░▓█  ▀█▓░▒█░    
░ ▒░   ░  ░ ▒▒   ▓▒█░ ▒▒▓  ▒  ▒▒   ▓▒█░░ ▒▓ ░▒▓░ ▒▒   ▓▒█░   ░ ▒░▒░▒░ ░▒▓███▀▒ ▒ ░    
░  ░      ░  ▒   ▒▒ ░ ░ ▒  ▒   ▒   ▒▒ ░  ░▒ ░ ▒░  ▒   ▒▒ ░     ░ ▒ ▒░ ▒░▒   ░  ░      
░      ░     ░   ▒    ░ ░  ░   ░   ▒     ░░   ░   ░   ▒      ░ ░ ░ ▒   ░    ░  ░ ░    
       ░         ░  ░   ░          ░  ░   ░           ░  ░       ░ ░   ░              
                     ░                                               ░         
]], Colors.CYAN)
    
    colored_print("Project Madara OBF v" .. CLI.version, Colors.BOLD)
    colored_print("Advanced Lua Obfuscator - Superior to Prometheus\n", Colors.GREEN)
end

-- Print help
local function print_help()
    print([[
USAGE:
    lua cli.lua [OPTIONS] <input_file>

OPTIONS:
    -o, --output <file>         Output file (default: input.obfuscated.lua)
    -p, --preset <preset>       Use predefined preset configuration
    -c, --config <file>         Load configuration from file
    -s, --security <level>      Security level: minimal, standard, enhanced, maximum
    --platform <platform>       Target platform: windows, linux, macos, generic
    --lua-version <version>     Lua version: 5.1, 5.2, 5.3, 5.4, LuaJIT
    
    --no-strings               Disable string obfuscation
    --no-control-flow          Disable control flow obfuscation
    --no-variables             Disable variable obfuscation
    --no-anti-debug            Disable anti-debugging features
    --enable-bytecode          Enable bytecode obfuscation
    
    --encryption-layers <n>    Number of encryption layers (1-5)
    --bogus-ratio <ratio>      Bogus code ratio (0.0-1.0)
    
    --list-presets             List all available presets
    --validate-config <file>   Validate configuration file
    --benchmark                Run obfuscation benchmark
    
    --verbose                  Enable verbose output
    --quiet                    Suppress non-error output
    --no-colors                Disable colored output
    --log-file <file>          Write logs to file
    
    -h, --help                 Show this help message
    -v, --version              Show version information

PRESETS:
    Security Levels: minimal, standard, enhanced, maximum
    Use Cases: commercial, gaming, research, embedded, web
    Competitive: anti-prometheus, anti-commercial

EXAMPLES:
    # Basic obfuscation with standard security
    lua cli.lua script.lua
    
    # Maximum security obfuscation
    lua cli.lua -s maximum script.lua
    
    # Gaming anti-cheat protection
    lua cli.lua -p gaming -o protected_game.lua game_script.lua
    
    # Custom configuration
    lua cli.lua -c my_config.lua script.lua
    
    # Surpass Prometheus obfuscator
    lua cli.lua -p anti-prometheus script.lua
]])
end

-- Parse command line arguments
local function parse_arguments(args)
    local config = {
        input_file = nil,
        output_file = nil,
        preset = nil,
        config_file = nil,
        security_level = nil,
        platform = nil,
        lua_version = nil,
        verbose = false,
        quiet = false,
        log_file = nil,
        custom_options = {}
    }
    
    local i = 1
    while i <= #args do
        local arg = args[i]
        
        if arg == "-h" or arg == "--help" then
            print_help()
            os.exit(0)
            
        elseif arg == "-v" or arg == "--version" then
            local version_info = MadaraOBF.get_version()
            print(version_info.name .. " v" .. version_info.version)
            print(version_info.description)
            os.exit(0)
            
        elseif arg == "-o" or arg == "--output" then
            i = i + 1
            config.output_file = args[i]
            
        elseif arg == "-p" or arg == "--preset" then
            i = i + 1
            config.preset = args[i]
            
        elseif arg == "-c" or arg == "--config" then
            i = i + 1
            config.config_file = args[i]
            
        elseif arg == "-s" or arg == "--security" then
            i = i + 1
            config.security_level = args[i]
            
        elseif arg == "--platform" then
            i = i + 1
            config.platform = args[i]
            
        elseif arg == "--lua-version" then
            i = i + 1
            config.lua_version = args[i]
            
        elseif arg == "--no-strings" then
            config.custom_options.string_obfuscation = {enabled = false}
            
        elseif arg == "--no-control-flow" then
            config.custom_options.control_flow = {enabled = false}
            
        elseif arg == "--no-variables" then
            config.custom_options.variable_obfuscation = {enabled = false}
            
        elseif arg == "--no-anti-debug" then
            config.custom_options.anti_debug = {enabled = false}
            
        elseif arg == "--enable-bytecode" then
            config.custom_options.bytecode = {enabled = true}
            
        elseif arg == "--encryption-layers" then
            i = i + 1
            local layers = tonumber(args[i])
            if layers and layers >= 1 and layers <= 5 then
                config.custom_options.string_obfuscation = {encryption_layers = layers}
            else
                colored_print("Error: Invalid encryption layers value", Colors.RED)
                os.exit(1)
            end
            
        elseif arg == "--bogus-ratio" then
            i = i + 1
            local ratio = tonumber(args[i])
            if ratio and ratio >= 0 and ratio <= 1 then
                config.custom_options.control_flow = {bogus_branches_ratio = ratio}
            else
                colored_print("Error: Invalid bogus ratio value", Colors.RED)
                os.exit(1)
            end
            
        elseif arg == "--list-presets" then
            print("Available Presets:")
            local presets = Presets.list()
            for _, preset in ipairs(presets) do
                print("  " .. preset.name .. " (" .. preset.category .. ") - " .. preset.description)
            end
            os.exit(0)
            
        elseif arg == "--validate-config" then
            i = i + 1
            -- TODO: Implement config validation
            print("Config validation not yet implemented")
            os.exit(0)
            
        elseif arg == "--benchmark" then
            -- TODO: Implement benchmark
            print("Benchmark not yet implemented")
            os.exit(0)
            
        elseif arg == "--verbose" then
            config.verbose = true
            
        elseif arg == "--quiet" then
            config.quiet = true
            
        elseif arg == "--no-colors" then
            use_colors = false
            
        elseif arg == "--log-file" then
            i = i + 1
            config.log_file = args[i]
            
        elseif arg:sub(1, 1) == "-" then
            colored_print("Error: Unknown option: " .. arg, Colors.RED)
            os.exit(1)
            
        else
            if config.input_file then
                colored_print("Error: Multiple input files specified", Colors.RED)
                os.exit(1)
            end
            config.input_file = arg
        end
        
        i = i + 1
    end
    
    return config
end

-- Check if file exists
local function file_exists(filename)
    local file = io.open(filename, "r")
    if file then
        file:close()
        return true
    end
    return false
end

-- Read file contents
local function read_file(filename)
    local file = io.open(filename, "r")
    if not file then
        return nil, "Cannot open file: " .. filename
    end
    
    local content = file:read("*all")
    file:close()
    return content
end

-- Write file contents
local function write_file(filename, content)
    local file = io.open(filename, "w")
    if not file then
        return false, "Cannot create file: " .. filename
    end
    
    file:write(content)
    file:close()
    return true
end

-- Main function
local function main(args)
    print_banner()
    
    local config = parse_arguments(args)
    
    -- Validate input file
    if not config.input_file then
        colored_print("Error: No input file specified", Colors.RED)
        print("Use --help for usage information")
        os.exit(1)
    end
    
    if not file_exists(config.input_file) then
        colored_print("Error: Input file not found: " .. config.input_file, Colors.RED)
        os.exit(1)
    end
    
    -- Set output file if not specified
    if not config.output_file then
        local base_name = config.input_file:match("(.+)%.lua$") or config.input_file
        config.output_file = base_name .. ".obfuscated.lua"
    end
    
    -- Read source code
    local source_code, err = read_file(config.input_file)
    if not source_code then
        colored_print("Error: " .. err, Colors.RED)
        os.exit(1)
    end
    
    -- Build obfuscator configuration
    local obf_config = {}
    
    -- Apply preset if specified
    if config.preset then
        local preset_config = Presets.get_for_platform(config.preset, config.platform)
        if not preset_config then
            colored_print("Error: Unknown preset: " .. config.preset, Colors.RED)
            os.exit(1)
        end
        obf_config = Utils.deep_merge(obf_config, preset_config)
    end
    
    -- Apply security level
    if config.security_level then
        obf_config.security_level = config.security_level
    end
    
    -- Apply other options
    if config.lua_version then
        obf_config.lua_version = config.lua_version
    end
    
    if config.platform then
        obf_config.target_environment = config.platform
    end
    
    -- Apply custom options
    obf_config = Utils.deep_merge(obf_config, config.custom_options)
    
    -- Set logging level
    if config.verbose then
        obf_config.log_level = "debug"
    elseif config.quiet then
        obf_config.log_level = "error"
    end
    
    -- Create obfuscator
    colored_print("Initializing Madara OBF...", Colors.BLUE)
    local obfuscator = MadaraOBF.new(obf_config)
    
    -- Process source code
    colored_print("Obfuscating " .. config.input_file .. "...", Colors.YELLOW)
    local start_time = os.clock()
    
    local obfuscated_code, metadata = obfuscator:process(source_code, config.input_file)
    
    if not obfuscated_code then
        colored_print("Error: Obfuscation failed: " .. tostring(metadata), Colors.RED)
        os.exit(1)
    end
    
    local end_time = os.clock()
    
    -- Write output
    local success, write_err = write_file(config.output_file, obfuscated_code)
    if not success then
        colored_print("Error: " .. write_err, Colors.RED)
        os.exit(1)
    end
    
    -- Print results
    colored_print("Obfuscation completed successfully!", Colors.GREEN)
    print("Input file:  " .. config.input_file)
    print("Output file: " .. config.output_file)
    print("Time taken:  " .. string.format("%.3f", end_time - start_time) .. " seconds")
    print("Original size: " .. #source_code .. " bytes")
    print("Obfuscated size: " .. #obfuscated_code .. " bytes")
    print("Size ratio: " .. string.format("%.1f%%", (#obfuscated_code / #source_code) * 100))
    
    if metadata and metadata.statistics then
        print("\nObfuscation Statistics:")
        local stats = metadata.statistics
        for key, value in pairs(stats) do
            print("  " .. key .. ": " .. tostring(value))
        end
    end
end

-- Run CLI
main(arg or {})
