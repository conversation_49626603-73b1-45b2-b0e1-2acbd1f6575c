-- Project Madara OBF - Advanced Logging System
-- Enhanced logging with multiple output targets and security-aware features

local Logger = {}

-- Log levels
local LOG_LEVELS = {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5
}

local LOG_LEVEL_NAMES = {
    [0] = "TRACE",
    [1] = "DEBUG", 
    [2] = "INFO",
    [3] = "WARN",
    [4] = "ERROR",
    [5] = "FATAL"
}

-- ANSI color codes for terminal output
local COLORS = {
    RESET = "\27[0m",
    BOLD = "\27[1m",
    RED = "\27[31m",
    GREEN = "\27[32m",
    YELLOW = "\27[33m",
    BLUE = "\27[34m",
    MAGENTA = "\27[35m",
    CYAN = "\27[36m",
    WHITE = "\27[37m",
    GRAY = "\27[90m"
}

local LEVEL_COLORS = {
    [0] = COLORS.GRAY,    -- TRACE
    [1] = COLORS.CYAN,    -- DEBUG
    [2] = COLORS.GREEN,   -- INFO
    [3] = COLORS.YELLOW,  -- WARN
    [4] = COLORS.RED,     -- ERROR
    [5] = COLORS.MAGENTA  -- FATAL
}

-- Create new logger instance
function Logger.new(level, config)
    level = level or "info"
    config = config or {}
    
    local numeric_level = LOG_LEVELS[string.upper(level)] or LOG_LEVELS.INFO
    
    local instance = {
        level = numeric_level,
        config = {
            use_colors = config.use_colors ~= false,
            timestamp = config.timestamp ~= false,
            show_level = config.show_level ~= false,
            show_source = config.show_source or false,
            output_file = config.output_file,
            max_file_size = config.max_file_size or 10 * 1024 * 1024, -- 10MB
            sanitize_output = config.sanitize_output ~= false
        },
        file_handle = nil,
        message_count = 0
    }
    
    setmetatable(instance, {__index = Logger})
    
    -- Open log file if specified
    if instance.config.output_file then
        instance:_open_log_file()
    end
    
    return instance
end

-- Open log file for writing
function Logger:_open_log_file()
    if self.file_handle then
        self.file_handle:close()
    end
    
    local file, err = io.open(self.config.output_file, "a")
    if not file then
        self:_write_console("ERROR", "Failed to open log file: " .. tostring(err))
        return false
    end
    
    self.file_handle = file
    return true
end

-- Close log file
function Logger:close()
    if self.file_handle then
        self.file_handle:close()
        self.file_handle = nil
    end
end

-- Check if log file needs rotation
function Logger:_check_file_rotation()
    if not self.file_handle or not self.config.max_file_size then
        return
    end
    
    local current_pos = self.file_handle:seek()
    if current_pos and current_pos > self.config.max_file_size then
        self.file_handle:close()
        
        -- Rotate log file
        local backup_name = self.config.output_file .. ".old"
        os.rename(self.config.output_file, backup_name)
        
        self:_open_log_file()
    end
end

-- Sanitize message to remove sensitive information
function Logger:_sanitize_message(message)
    if not self.config.sanitize_output then
        return message
    end
    
    -- Remove potential file paths
    message = message:gsub("[A-Za-z]:[/\\][^%s]*", "[PATH]")
    message = message:gsub("/[^%s]*", "[PATH]")
    
    -- Remove potential API keys or tokens
    message = message:gsub("[A-Za-z0-9]{32,}", "[TOKEN]")
    
    -- Remove potential IP addresses
    message = message:gsub("%d+%.%d+%.%d+%.%d+", "[IP]")
    
    return message
end

-- Format timestamp
function Logger:_format_timestamp()
    if not self.config.timestamp then
        return ""
    end
    
    return "[" .. os.date("%Y-%m-%d %H:%M:%S") .. "] "
end

-- Format log message
function Logger:_format_message(level, message, source_info)
    local parts = {}
    
    -- Timestamp
    table.insert(parts, self:_format_timestamp())
    
    -- Log level
    if self.config.show_level then
        table.insert(parts, "[" .. LOG_LEVEL_NAMES[level] .. "] ")
    end
    
    -- Source information
    if self.config.show_source and source_info then
        table.insert(parts, "[" .. source_info .. "] ")
    end
    
    -- Message
    table.insert(parts, self:_sanitize_message(tostring(message)))
    
    return table.concat(parts)
end

-- Write to console with colors
function Logger:_write_console(level_name, formatted_message)
    local level_num = LOG_LEVELS[level_name]
    local color = ""
    local reset = ""
    
    if self.config.use_colors then
        color = LEVEL_COLORS[level_num] or ""
        reset = COLORS.RESET
    end
    
    io.write(color .. formatted_message .. reset .. "\n")
    io.flush()
end

-- Write to file
function Logger:_write_file(formatted_message)
    if not self.file_handle then
        return
    end
    
    self:_check_file_rotation()
    
    self.file_handle:write(formatted_message .. "\n")
    self.file_handle:flush()
end

-- Core logging function
function Logger:_log(level, message, source_info)
    if level < self.level then
        return
    end
    
    local level_name = LOG_LEVEL_NAMES[level]
    local formatted_message = self:_format_message(level, message, source_info)
    
    -- Write to console
    self:_write_console(level_name, formatted_message)
    
    -- Write to file if configured
    if self.file_handle then
        self:_write_file(formatted_message)
    end
    
    self.message_count = self.message_count + 1
end

-- Get source information for debugging
function Logger:_get_source_info()
    if not self.config.show_source then
        return nil
    end
    
    local info = debug.getinfo(4, "Sl")
    if info then
        local source = info.source:match("([^/\\]+)$") or info.source
        return source .. ":" .. (info.currentline or "?")
    end
    
    return nil
end

-- Public logging methods
function Logger:trace(message)
    self:_log(LOG_LEVELS.TRACE, message, self:_get_source_info())
end

function Logger:debug(message)
    self:_log(LOG_LEVELS.DEBUG, message, self:_get_source_info())
end

function Logger:info(message)
    self:_log(LOG_LEVELS.INFO, message, self:_get_source_info())
end

function Logger:warn(message)
    self:_log(LOG_LEVELS.WARN, message, self:_get_source_info())
end

function Logger:error(message)
    self:_log(LOG_LEVELS.ERROR, message, self:_get_source_info())
end

function Logger:fatal(message)
    self:_log(LOG_LEVELS.FATAL, message, self:_get_source_info())
end

-- Set log level
function Logger:set_level(level)
    if type(level) == "string" then
        level = LOG_LEVELS[string.upper(level)]
    end
    
    if level and level >= 0 and level <= 5 then
        self.level = level
    end
end

-- Get current log level
function Logger:get_level()
    return LOG_LEVEL_NAMES[self.level]
end

-- Get message count
function Logger:get_message_count()
    return self.message_count
end

-- Export log levels for external use
Logger.LEVELS = LOG_LEVELS

return Logger
